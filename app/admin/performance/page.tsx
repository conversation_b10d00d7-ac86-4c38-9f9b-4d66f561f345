'use client';

import { useState } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { usePerformance, usePerformanceActions } from '@/hooks/usePerformance';
import { ErrorBoundary } from '@/components/error/ErrorBoundary';
import AdminLayout from '@/components/layout/AdminLayout';
import {
  Activity,
  Server,
  Database,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Monitor,
  HardDrive,
  Cpu,
  TrendingUp,
  Shield,
  Zap
} from 'lucide-react';

function PerformancePage() {
  const { adminUser, hasPermission, loading } = useAuth();
  const [timeRange, setTimeRange] = useState(3600000); // 1 hour default
  const [includeCache, setIncludeCache] = useState(true);
  const [includeDetails, setIncludeDetails] = useState(false); // Simplified view by default

  const { data: performanceData, isLoading, error, refetch } = usePerformance({
    timeRange,
    includeCache,
    includeDetails,
  });

  const performanceActions = usePerformanceActions();

  // Helper function to format uptime
  const formatUptime = (uptimeMs: number) => {
    const seconds = Math.floor(uptimeMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // Check permissions
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!adminUser || !hasPermission('performance', 'read')) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to view performance data.</p>
        </div>
      </div>
    );
  }

  const handleAction = (action: string) => {
    performanceActions.mutate({ action }, {
      onSuccess: () => {
        refetch();
      }
    });
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />;
    if (score >= 60) return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    return <XCircle className="h-5 w-5 text-red-600" />;
  };



  const formatBytes = (bytes: number) => {
    return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="text-center">
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to Load Performance Data</h2>
          <p className="text-gray-600 mb-4">{error.message}</p>
          <button
            onClick={() => refetch()}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">System Health Overview</h1>
            <p className="text-gray-600">Monitor your website's performance and system status</p>
          </div>

          <div className="flex items-center space-x-4">
            {/* Time Range Selector */}
            <label htmlFor="time-range-selector" className="text-sm font-medium text-gray-700">
              View data from:
            </label>
            <select
              id="time-range-selector"
              value={timeRange}
              onChange={(e) => setTimeRange(Number(e.target.value))}
              className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
            <option value={3600000}>Last hour</option>
            <option value={21600000}>Last 6 hours</option>
            <option value={86400000}>Last 24 hours</option>
          </select>

            <button
              onClick={() => refetch()}
              disabled={isLoading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Update
            </button>
          </div>
        </div>

        {performanceData && (
          <>
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Overall Health */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {getHealthScoreIcon(performanceData.summary.healthScore)}
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Overall Health</dt>
                      <dd className={`text-2xl font-bold ${getHealthScoreColor(performanceData.summary.healthScore)}`}>
                        {performanceData.summary.healthScore >= 80 ? 'Excellent' :
                         performanceData.summary.healthScore >= 60 ? 'Good' : 'Needs Attention'}
                      </dd>
                      <dd className={`text-sm ${getHealthScoreColor(performanceData.summary.healthScore)}`}>
                        {performanceData.summary.healthScore}% score
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Website Speed */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Zap className={`h-5 w-5 ${performanceData.summary.averageResponseTime < 500 ? 'text-green-600' :
                      performanceData.summary.averageResponseTime < 1000 ? 'text-yellow-600' : 'text-red-600'}`} />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Website Speed</dt>
                      <dd className={`text-2xl font-bold ${performanceData.summary.averageResponseTime < 500 ? 'text-green-600' :
                        performanceData.summary.averageResponseTime < 1000 ? 'text-yellow-600' : 'text-red-600'}`}>
                        {performanceData.summary.averageResponseTime < 500 ? 'Fast' :
                         performanceData.summary.averageResponseTime < 1000 ? 'Moderate' : 'Slow'}
                      </dd>
                      <dd className="text-sm text-gray-500">
                        {performanceData.summary.averageResponseTime.toFixed(0)}ms average
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* System Status */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Shield className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">System Status</dt>
                      <dd className="text-2xl font-bold text-green-600">
                        Running
                      </dd>
                      <dd className="text-sm text-gray-500">
                        {formatUptime(performanceData.systemInfo.uptime)} uptime
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

          </div>

        {/* System Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  <Monitor className="inline h-5 w-5 mr-2" />
                  System Information
                </h3>
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Node Version</dt>
                    <dd className="mt-1 text-sm text-gray-900">{performanceData.systemInfo.nodeVersion}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Platform</dt>
                    <dd className="mt-1 text-sm text-gray-900">{performanceData.systemInfo.platform}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Uptime</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatUptime(performanceData.systemInfo.uptime)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(performanceData.systemInfo.timestamp).toLocaleString()}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Memory Usage */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  <HardDrive className="inline h-5 w-5 mr-2" />
                  Server Memory Usage
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Node.js server memory consumption (not browser memory)
                </p>
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">RSS</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatBytes(performanceData.systemInfo.serverMemoryUsage.rss)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Heap Total</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatBytes(performanceData.systemInfo.serverMemoryUsage.heapTotal)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Heap Used</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatBytes(performanceData.systemInfo.serverMemoryUsage.heapUsed)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">External</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatBytes(performanceData.systemInfo.serverMemoryUsage.external)}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>

        {/* Key Performance Indicators */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-6">
              <TrendingUp className="inline h-5 w-5 mr-2" />
              Key Performance Indicators
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className={`text-3xl font-bold mb-2 ${performanceData.summary.averageResponseTime < 500 ? 'text-green-600' :
                  performanceData.summary.averageResponseTime < 1000 ? 'text-yellow-600' : 'text-red-600'}`}>
                  {performanceData.summary.averageResponseTime.toFixed(0)}ms
                </div>
                <div className="text-sm font-medium text-gray-900">Average Response Time</div>
                <div className="text-xs text-gray-500 mt-1">
                  {performanceData.summary.averageResponseTime < 500 ? 'Excellent - Very fast' :
                   performanceData.summary.averageResponseTime < 1000 ? 'Good - Acceptable speed' : 'Slow - Needs improvement'}
                </div>
              </div>

              <div className="text-center">
                <div className={`text-3xl font-bold mb-2 ${performanceData.summary.averageDbQueryTime < 100 ? 'text-green-600' :
                  performanceData.summary.averageDbQueryTime < 300 ? 'text-yellow-600' : 'text-red-600'}`}>
                  {performanceData.summary.averageDbQueryTime.toFixed(0)}ms
                </div>
                <div className="text-sm font-medium text-gray-900">Database Speed</div>
                <div className="text-xs text-gray-500 mt-1">
                  {performanceData.summary.averageDbQueryTime < 100 ? 'Excellent - Very fast queries' :
                   performanceData.summary.averageDbQueryTime < 300 ? 'Good - Normal speed' : 'Slow - Database optimization needed'}
                </div>
              </div>

              <div className="text-center">
                <div className={`text-3xl font-bold mb-2 ${performanceData.summary.averagePageLoadTime < 1000 ? 'text-green-600' :
                  performanceData.summary.averagePageLoadTime < 2000 ? 'text-yellow-600' : 'text-red-600'}`}>
                  {performanceData.summary.averagePageLoadTime.toFixed(0)}ms
                </div>
                <div className="text-sm font-medium text-gray-900">Page Load Speed</div>
                <div className="text-xs text-gray-500 mt-1">
                  {performanceData.summary.averagePageLoadTime < 1000 ? 'Excellent - Very fast loading' :
                   performanceData.summary.averagePageLoadTime < 2000 ? 'Good - Acceptable loading' : 'Slow - Optimization needed'}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* System Health Recommendations */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              <Activity className="inline h-5 w-5 mr-2" />
              System Health Recommendations
            </h3>
            <div className="space-y-4">
              {performanceData.summary.averageResponseTime > 1000 && (
                <div className="flex items-start space-x-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-yellow-800">Website Speed Could Be Improved</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      Your website is loading slower than optimal. Consider optimizing images, enabling caching, or upgrading your hosting plan.
                    </p>
                  </div>
                </div>
              )}

              {performanceData.summary.averageDbQueryTime > 300 && (
                <div className="flex items-start space-x-3 p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <Database className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-orange-800">Database Performance Needs Attention</h4>
                    <p className="text-sm text-orange-700 mt-1">
                      Database queries are taking longer than expected. Consider clearing old data or optimizing database queries.
                    </p>
                  </div>
                </div>
              )}

              {performanceData.summary.averageResponseTime <= 500 && performanceData.summary.averageDbQueryTime <= 100 && (
                <div className="flex items-start space-x-3 p-4 bg-green-50 rounded-lg border border-green-200">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-green-800">Excellent Performance</h4>
                    <p className="text-sm text-green-700 mt-1">
                      Your website is performing excellently! All systems are running smoothly and efficiently.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Maintenance Actions */}
        {hasPermission('performance', 'update') && (
          <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  <Cpu className="inline h-5 w-5 mr-2" />
                  System Maintenance
                </h3>
                <p className="text-sm text-gray-600 mb-6">
                  Use these tools to optimize your website's performance. These actions are safe and can help improve loading speeds.
                </p>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Clear Website Cache</h4>
                      <p className="text-sm text-gray-600">Clears stored data to ensure visitors see the latest content</p>
                    </div>
                    <button
                      onClick={() => handleAction('clear_cache')}
                      disabled={performanceActions.isPending}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                    >
                      <Database className="h-4 w-4 mr-2" />
                      Clear Cache
                    </button>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Clean Up Old Data</h4>
                      <p className="text-sm text-gray-600">Removes expired temporary files to free up space</p>
                    </div>
                    <button
                      onClick={() => handleAction('cleanup_cache')}
                      disabled={performanceActions.isPending}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Clean Up
                    </button>
                  </div>
                </div>
              </div>
          </div>
        )}

        {/* Recent Activity Summary */}
        {includeDetails && performanceData.details?.slowestOperations && performanceData.details.slowestOperations.length > 0 && (
          <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    <Server className="inline h-5 w-5 mr-2" />
                    Recent Activity Summary
                  </h3>
                  <button
                    onClick={() => setIncludeDetails(!includeDetails)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {includeDetails ? 'Hide Details' : 'Show Details'}
                  </button>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Operations that took longer than usual to complete
                </p>
                <div className="space-y-3">
                  {performanceData.details.slowestOperations.slice(0, 5).map((operation, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {operation.operation.replace(/^\/api\//, '').replace(/\//g, ' › ')}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(operation.timestamp).toLocaleString()}
                        </div>
                      </div>
                      <div className={`text-sm font-medium ${operation.duration > 1000 ? 'text-red-600' :
                        operation.duration > 500 ? 'text-yellow-600' : 'text-gray-600'}`}>
                        {operation.duration.toFixed(0)}ms
                      </div>
                    </div>
                  ))}
                </div>
            </div>
          </div>
        )}
      </>
    )}
      </div>
    </AdminLayout>
  );
}

export default function PerformancePageWithErrorBoundary() {
  return (
    <ErrorBoundary>
      <PerformancePage />
    </ErrorBoundary>
  );
}
