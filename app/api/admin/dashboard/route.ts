import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { measureApiCall } from '@/lib/performance-monitor';

interface DashboardStats {
  totalTrips: number;
  activeTrips: number;
  draftTrips: number;
  totalBlogs: number;
  publishedBlogs: number;
  draftBlogs: number;
  totalInquiries: number;
  newInquiries: number;
  respondedInquiries: number;
  totalPhotos: number;
  recentTrips: Array<{
    id: string;
    title: string;
    slug: string;
    destination: string;
    created_at: string | null;
    is_active: boolean | null;
  }>;
  recentBlogs: Array<{
    id: string;
    title: string;
    slug: string;
    created_at: string | null;
    is_published: boolean | null;
  }>;
  recentInquiries: Array<{
    id: string;
    name: string;
    email: string;
    subject: string | null;
    status: string | null;
    created_at: string | null;
  }>;
}

export async function GET(_request: NextRequest) {
  return measureApiCall('admin_dashboard_get', async () => {
    try {
      // Verify admin authentication - any admin can access dashboard
      const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Check user permissions to determine what data to fetch
    const { hasPermission } = await import('@/lib/auth');
    const userRoles = user.roles;

    const canReadTrips = hasPermission(userRoles, 'trips', 'read');
    const canReadBlogs = hasPermission(userRoles, 'blog', 'read');
    const canReadInquiries = hasPermission(userRoles, 'inquiries', 'read');
    const canReadPhotos = hasPermission(userRoles, 'trip_photos', 'read');

    // Initialize stats object
    const stats: DashboardStats = {
      totalTrips: 0,
      activeTrips: 0,
      draftTrips: 0,
      totalBlogs: 0,
      publishedBlogs: 0,
      draftBlogs: 0,
      totalInquiries: 0,
      newInquiries: 0,
      respondedInquiries: 0,
      totalPhotos: 0,
      recentTrips: [],
      recentBlogs: [],
      recentInquiries: []
    };

    // Fetch trips data conditionally
    if (canReadTrips) {
      try {
        // Use a single query with aggregation for better performance
        const [aggregateResult, recentTripsResult] = await Promise.all([
          supabase.rpc('get_trips_dashboard_stats' as any),
          supabase
            .from('trips')
            .select('id, title, slug, destination, created_at, is_active')
            .order('created_at', { ascending: false })
            .limit(5)
        ]);

        if (aggregateResult.data && Array.isArray(aggregateResult.data) && aggregateResult.data.length > 0) {
          const tripStats = (aggregateResult.data as any[])[0];
          stats.totalTrips = tripStats.total_trips || 0;
          stats.activeTrips = tripStats.active_trips || 0;
          stats.draftTrips = tripStats.draft_trips || 0;
        }
        stats.recentTrips = recentTripsResult.data || [];
      } catch (error) {
        console.error('Error fetching trips data:', error);
        // Fallback to individual queries if RPC function doesn't exist
        try {
          const [tripsResult, activeTripsResult, draftTripsResult] = await Promise.all([
            supabase.from('trips').select('id', { count: 'exact', head: true }),
            supabase.from('trips').select('id', { count: 'exact', head: true }).eq('is_active', true),
            supabase.from('trips').select('id', { count: 'exact', head: true }).eq('is_active', false)
          ]);
          stats.totalTrips = tripsResult.count || 0;
          stats.activeTrips = activeTripsResult.count || 0;
          stats.draftTrips = draftTripsResult.count || 0;
        } catch (fallbackError) {
          console.error('Error in trips fallback query:', fallbackError);
        }
      }
    }

    // Fetch blogs data conditionally
    if (canReadBlogs) {
      try {
        // Use a single query with aggregation for better performance
        const [aggregateResult, recentBlogsResult] = await Promise.all([
          supabase.rpc('get_blogs_dashboard_stats' as any),
          supabase
            .from('blog_posts')
            .select('id, title, slug, created_at, is_published')
            .order('created_at', { ascending: false })
            .limit(5)
        ]);

        if (aggregateResult.data && Array.isArray(aggregateResult.data) && aggregateResult.data.length > 0) {
          const blogStats = (aggregateResult.data as any[])[0];
          stats.totalBlogs = blogStats.total_blogs || 0;
          stats.publishedBlogs = blogStats.published_blogs || 0;
          stats.draftBlogs = blogStats.draft_blogs || 0;
        }
        stats.recentBlogs = recentBlogsResult.data || [];
      } catch (error) {
        console.error('Error fetching blogs data:', error);
        // Fallback to individual queries if RPC function doesn't exist
        try {
          const [blogsResult, publishedBlogsResult, draftBlogsResult] = await Promise.all([
            supabase.from('blog_posts').select('id', { count: 'exact', head: true }),
            supabase.from('blog_posts').select('id', { count: 'exact', head: true }).eq('is_published', true),
            supabase.from('blog_posts').select('id', { count: 'exact', head: true }).eq('is_published', false)
          ]);
          stats.totalBlogs = blogsResult.count || 0;
          stats.publishedBlogs = publishedBlogsResult.count || 0;
          stats.draftBlogs = draftBlogsResult.count || 0;
        } catch (fallbackError) {
          console.error('Error in blogs fallback query:', fallbackError);
        }
      }
    }

    // Fetch inquiries data conditionally
    if (canReadInquiries) {
      try {
        // Use a single query with aggregation for better performance
        const [aggregateResult, recentInquiriesResult] = await Promise.all([
          supabase.rpc('get_inquiries_dashboard_stats' as any),
          supabase
            .from('inquiries')
            .select('id, name, email, subject, status, created_at')
            .order('created_at', { ascending: false })
            .limit(5)
        ]);

        if (aggregateResult.data && Array.isArray(aggregateResult.data) && aggregateResult.data.length > 0) {
          const inquiryStats = (aggregateResult.data as any[])[0];
          stats.totalInquiries = inquiryStats.total_inquiries || 0;
          stats.newInquiries = inquiryStats.new_inquiries || 0;
          stats.respondedInquiries = inquiryStats.responded_inquiries || 0;
        }
        stats.recentInquiries = recentInquiriesResult.data || [];
      } catch (error) {
        console.error('Error fetching inquiries data:', error);
        // Fallback to individual queries if RPC function doesn't exist
        try {
          const [inquiriesResult, newInquiriesResult, respondedInquiriesResult] = await Promise.all([
            supabase.from('inquiries').select('id', { count: 'exact', head: true }),
            supabase.from('inquiries').select('id', { count: 'exact', head: true }).eq('status', 'new'),
            supabase.from('inquiries').select('id', { count: 'exact', head: true }).eq('status', 'resolved')
          ]);
          stats.totalInquiries = inquiriesResult.count || 0;
          stats.newInquiries = newInquiriesResult.count || 0;
          stats.respondedInquiries = respondedInquiriesResult.count || 0;
        } catch (fallbackError) {
          console.error('Error in inquiries fallback query:', fallbackError);
        }
      }
    }

    // Fetch photos data conditionally
    if (canReadPhotos) {
      try {
        const photosResult = await supabase
          .from('trip_photos_details')
          .select('id', { count: 'exact', head: true });

        stats.totalPhotos = photosResult.count || 0;
      } catch (error) {
        console.error('Error fetching photos data:', error);
      }
    }

    // Dashboard access is a read operation - no audit logging needed

    return NextResponse.json({ stats }, {
      headers: {
        'Cache-Control': 'private, max-age=120, stale-while-revalidate=60', // Cache for 2 minutes, allow stale for 1 minute
        'Vary': 'Authorization', // Vary by user since data is permission-based
      },
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
  }, '/api/admin/dashboard');
}
