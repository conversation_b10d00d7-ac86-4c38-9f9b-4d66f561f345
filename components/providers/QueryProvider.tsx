'use client';

import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from '@tanstack/react-query';
import { useState, ReactNode } from 'react';

interface QueryProviderProps {
  children: ReactNode;
}

export default function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () => {
      try {
        return new QueryClient({
          defaultOptions: {
            queries: {
              // Differentiated stale times based on data type
              staleTime: 2 * 60 * 1000, // 2 minutes default - shorter for admin data
              gcTime: 15 * 60 * 1000, // 15 minutes - longer cache retention
              retry: (failureCount, error: unknown) => {
                // Don't retry on 4xx errors
                const errorWithStatus = error as { status?: number };
                if (errorWithStatus?.status && errorWithStatus.status >= 400 && errorWithStatus.status < 500) {
                  return false;
                }
                // Retry up to 2 times for other errors (less aggressive)
                return failureCount < 2;
              },
              refetchOnWindowFocus: false, // Disable for admin panels - use manual refresh instead
              refetchOnMount: true,
              refetchInterval: false, // Disable automatic refetching - use smart real-time updates instead
              refetchOnReconnect: true, // Refetch when network reconnects
              // Enable background refetching for better UX
              refetchIntervalInBackground: false,
              // Retry delay with exponential backoff
              retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
            },
            mutations: {
              retry: false,
              // Add global mutation settings for better UX
              onError: (error) => {
                if (process.env.NODE_ENV === 'development') {
                  console.error('Mutation error:', error);
                }
              },
            },
          },
          // Add query cache configuration for better memory management
          queryCache: new QueryCache({
            onError: (error, query) => {
              if (process.env.NODE_ENV === 'development') {
                console.error('Query error:', error, 'Query key:', query.queryKey);
              }
            },
          }),
          // Add mutation cache for better error handling
          mutationCache: new MutationCache({
            onError: (error, variables, context, mutation) => {
              if (process.env.NODE_ENV === 'development') {
                console.error('Mutation cache error:', error, 'Variables:', variables);
              }
            },
          }),
        });
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to create QueryClient:', error);
        }
        // Return a basic QueryClient as fallback
        return new QueryClient();
      }
    }
  );

  if (!queryClient) {
    if (process.env.NODE_ENV === 'development') {
      console.error('QueryClient is undefined');
    }
    return <>{children}</>;
  }

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
