'use client';

import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from '@tanstack/react-query';
import { useState, ReactNode } from 'react';

interface QueryProviderProps {
  children: ReactNode;
}

export default function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () => {
      try {
        return new QueryClient({
          defaultOptions: {
            queries: {
              // Conservative stale time for admin data freshness
              staleTime: 2 * 60 * 1000, // 2 minutes - balance between freshness and performance
              gcTime: 15 * 60 * 1000, // 15 minutes - longer cache retention for better UX
              retry: (failureCount, error: unknown) => {
                // Don't retry on 4xx errors (client errors)
                const errorWithStatus = error as { status?: number };
                if (errorWithStatus?.status && errorWithStatus.status >= 400 && errorWithStatus.status < 500) {
                  return false;
                }
                // Retry up to 2 times for server errors and network issues
                return failureCount < 2;
              },
              refetchOnWindowFocus: false, // Disable for admin panels - use manual refresh instead
              refetchOnMount: true,
              refetchInterval: false, // Disable automatic polling - use manual refresh for admin data
              refetchOnReconnect: true, // Refetch when network reconnects
              // Disable background refetching for admin panels
              refetchIntervalInBackground: false,
              // Exponential backoff for retry delays
              retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
            },
            mutations: {
              retry: false,
              // Add global mutation settings for better UX
              onError: (error) => {
                if (process.env.NODE_ENV === 'development') {
                  console.error('Mutation error:', error);
                }
              },
            },
          },
          // Add query cache configuration for better memory management
          queryCache: new QueryCache({
            onError: (error, query) => {
              if (process.env.NODE_ENV === 'development') {
                console.error('Query error:', error, 'Query key:', query.queryKey);
              }
            },
          }),
          // Add mutation cache for better error handling
          mutationCache: new MutationCache({
            onError: (error, variables, context, mutation) => {
              if (process.env.NODE_ENV === 'development') {
                console.error('Mutation cache error:', error, 'Variables:', variables);
              }
            },
          }),
        });
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to create QueryClient:', error);
        }
        // Return a basic QueryClient as fallback
        return new QueryClient();
      }
    }
  );

  if (!queryClient) {
    if (process.env.NODE_ENV === 'development') {
      console.error('QueryClient is undefined');
    }
    return <>{children}</>;
  }

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
