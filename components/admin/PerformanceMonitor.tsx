'use client';

import React, { useState } from 'react';
import { useAdminPerformanceMonitoring } from '@/hooks/useAdminPerformanceMonitoring';
import { useToast } from '@/hooks/useToast';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Info,
  Minimize2,
  Maximize2,
  RefreshCw
} from 'lucide-react';

interface PerformanceMonitorProps {
  enabled?: boolean;
  showInProduction?: boolean;
}

export default function PerformanceMonitor({ 
  enabled = true, 
  showInProduction = false 
}: PerformanceMonitorProps) {
  const toast = useToast();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Only show in development unless explicitly enabled for production
  const shouldShow = enabled && (process.env.NODE_ENV === 'development' || showInProduction);

  const { metrics, alerts, runPerformanceCheck, clearAlerts, isMonitoring } = useAdminPerformanceMonitoring({
    enabled: shouldShow,
    onAlert: (alert) => {
      if (alert.type === 'error') {
        toast.error(`Performance Issue: ${alert.message}`);
      } else if (alert.type === 'warning') {
        toast.error(`Performance Warning: ${alert.message}`);
      }
    },
  });

  if (!shouldShow) return null;

  const getAlertIcon = (type: 'warning' | 'error' | 'info') => {
    switch (type) {
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  const formatMetric = (value: number, unit: string = 'ms') => {
    if (unit === 'bytes') {
      if (value > 1024 * 1024) {
        return `${(value / (1024 * 1024)).toFixed(2)} MB`;
      } else if (value > 1024) {
        return `${(value / 1024).toFixed(2)} KB`;
      }
      return `${value} B`;
    }
    
    if (unit === 'ms' && value > 1000) {
      return `${(value / 1000).toFixed(2)}s`;
    }
    
    return `${value.toFixed(2)}${unit}`;
  };

  const getPerformanceScore = () => {
    if (!metrics.loadTime) return 0;
    
    let score = 100;
    
    // Deduct points for slow metrics
    if (metrics.loadTime > 3000) score -= 20;
    if (metrics.firstPaint > 1000) score -= 15;
    if (metrics.largestContentfulPaint > 2500) score -= 15;
    if (metrics.firstInputDelay > 100) score -= 10;
    if (metrics.cumulativeLayoutShift > 0.1) score -= 10;
    if (metrics.memoryUsage > 100 * 1024 * 1024) score -= 10;
    if (alerts.length > 0) score -= alerts.length * 5;
    
    return Math.max(0, score);
  };

  const performanceScore = getPerformanceScore();
  const scoreColor = performanceScore >= 80 ? 'text-green-500' : 
                    performanceScore >= 60 ? 'text-yellow-500' : 'text-red-500';

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Compact view */}
      {!isExpanded && (
        <div 
          className="bg-white rounded-lg shadow-lg border p-3 cursor-pointer hover:shadow-xl transition-shadow"
          onClick={() => setIsExpanded(true)}
        >
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-500" />
            <span className={`font-semibold ${scoreColor}`}>
              {performanceScore}
            </span>
            {alerts.length > 0 && (
              <div className="flex items-center space-x-1">
                <AlertTriangle className="w-4 h-4 text-yellow-500" />
                <span className="text-sm text-yellow-600">{alerts.length}</span>
              </div>
            )}
            <Maximize2 className="w-4 h-4 text-gray-400" />
          </div>
        </div>
      )}

      {/* Expanded view */}
      {isExpanded && (
        <div className="bg-white rounded-lg shadow-xl border w-96 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="bg-gray-50 px-4 py-3 border-b flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-blue-500" />
              <h3 className="font-semibold text-gray-900">Performance Monitor</h3>
              <span className={`font-bold ${scoreColor}`}>
                {performanceScore}/100
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={runPerformanceCheck}
                className="p-1 hover:bg-gray-200 rounded"
                title="Refresh metrics"
              >
                <RefreshCw className="w-4 h-4 text-gray-600" />
              </button>
              <button
                onClick={() => setIsExpanded(false)}
                className="p-1 hover:bg-gray-200 rounded"
              >
                <Minimize2 className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-4 max-h-80 overflow-y-auto">
            {/* Alerts */}
            {alerts.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Performance Alerts</h4>
                  <button
                    onClick={clearAlerts}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Clear
                  </button>
                </div>
                <div className="space-y-2">
                  {alerts.slice(0, 3).map((alert, index) => (
                    <div key={index} className="flex items-start space-x-2 p-2 bg-gray-50 rounded">
                      {getAlertIcon(alert.type)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900 truncate">{alert.message}</p>
                        <p className="text-xs text-gray-600 mt-1">{alert.suggestion}</p>
                      </div>
                    </div>
                  ))}
                  {alerts.length > 3 && (
                    <p className="text-sm text-gray-600">
                      +{alerts.length - 3} more alerts
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Key Metrics */}
            <div className="mb-4">
              <h4 className="font-medium text-gray-900 mb-2">Key Metrics</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Load Time:</span>
                  <span className={metrics.loadTime > 3000 ? 'text-red-600' : 'text-green-600'}>
                    {formatMetric(metrics.loadTime || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">First Paint:</span>
                  <span className={metrics.firstPaint > 1000 ? 'text-red-600' : 'text-green-600'}>
                    {formatMetric(metrics.firstPaint || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Memory:</span>
                  <span className={metrics.memoryUsage > 100 * 1024 * 1024 ? 'text-red-600' : 'text-green-600'}>
                    {formatMetric(metrics.memoryUsage || 0, 'bytes')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Queries:</span>
                  <span className={metrics.queryCount > 50 ? 'text-red-600' : 'text-green-600'}>
                    {metrics.queryCount || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Toggle detailed view */}
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-sm text-blue-600 hover:text-blue-800 mb-2"
            >
              {showDetails ? 'Hide' : 'Show'} detailed metrics
            </button>

            {/* Detailed Metrics */}
            {showDetails && (
              <div className="space-y-2 text-sm">
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">DOM Ready:</span>
                    <span>{formatMetric(metrics.domContentLoaded || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">LCP:</span>
                    <span>{formatMetric(metrics.largestContentfulPaint || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">FID:</span>
                    <span>{formatMetric(metrics.firstInputDelay || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">CLS:</span>
                    <span>{(metrics.cumulativeLayoutShift || 0).toFixed(3)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Cache Size:</span>
                    <span>{formatMetric(metrics.cacheSize || 0, 'bytes')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Stale Queries:</span>
                    <span>{metrics.staleCacheEntries || 0}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Status */}
            <div className="mt-4 pt-3 border-t">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Monitoring:</span>
                <div className="flex items-center space-x-1">
                  {isMonitoring ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-green-600">Active</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="w-4 h-4 text-red-500" />
                      <span className="text-red-600">Inactive</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
