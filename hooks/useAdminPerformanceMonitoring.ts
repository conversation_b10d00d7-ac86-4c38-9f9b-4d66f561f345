import { useEffect, useRef, useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Enhanced performance monitoring specifically for admin panels
 * Tracks metrics, detects performance issues, and provides optimization suggestions
 */

interface PerformanceMetrics {
  // Page load metrics
  loadTime: number;
  domContentLoaded: number;
  firstPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  
  // React Query metrics
  queryCount: number;
  cacheSize: number;
  staleCacheEntries: number;
  
  // Memory metrics
  memoryUsage: number;
  
  // Network metrics
  networkRequests: number;
  failedRequests: number;
  
  // User interaction metrics
  timeToInteractive: number;
  
  // Custom admin metrics
  adminPageLoadTime: number;
  listRenderTime: number;
  formSubmissionTime: number;
}

interface PerformanceAlert {
  type: 'warning' | 'error' | 'info';
  message: string;
  metric: keyof PerformanceMetrics;
  value: number;
  threshold: number;
  suggestion: string;
}

interface UseAdminPerformanceMonitoringOptions {
  enabled?: boolean;
  alertThresholds?: Partial<Record<keyof PerformanceMetrics, number>>;
  onAlert?: (alert: PerformanceAlert) => void;
  collectInterval?: number; // How often to collect metrics (ms)
  reportInterval?: number; // How often to report to server (ms)
}

// Default performance thresholds for admin panels
const DEFAULT_THRESHOLDS: Record<keyof PerformanceMetrics, number> = {
  loadTime: 3000, // 3 seconds
  domContentLoaded: 2000, // 2 seconds
  firstPaint: 1000, // 1 second
  largestContentfulPaint: 2500, // 2.5 seconds
  firstInputDelay: 100, // 100ms
  cumulativeLayoutShift: 0.1, // 0.1 CLS score
  queryCount: 50, // Max 50 active queries
  cacheSize: 10 * 1024 * 1024, // 10MB cache size
  staleCacheEntries: 20, // Max 20 stale entries
  memoryUsage: 100 * 1024 * 1024, // 100MB memory usage
  networkRequests: 100, // Max 100 concurrent requests
  failedRequests: 5, // Max 5 failed requests
  timeToInteractive: 3500, // 3.5 seconds
  adminPageLoadTime: 2000, // 2 seconds for admin pages
  listRenderTime: 500, // 500ms for list rendering
  formSubmissionTime: 1000, // 1 second for form submission
};

export function useAdminPerformanceMonitoring({
  enabled = true,
  alertThresholds = {},
  onAlert,
  collectInterval = 5000, // 5 seconds
  reportInterval = 60000, // 1 minute
}: UseAdminPerformanceMonitoringOptions = {}) {
  const queryClient = useQueryClient();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({} as PerformanceMetrics);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const metricsRef = useRef<PerformanceMetrics>({} as PerformanceMetrics);
  const collectIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reportIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const thresholds = { ...DEFAULT_THRESHOLDS, ...alertThresholds };

  // Collect performance metrics
  const collectMetrics = useCallback(async () => {
    if (!enabled) return;

    try {
      const newMetrics: Partial<PerformanceMetrics> = {};

      // Web Vitals and Navigation Timing
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paint = performance.getEntriesByType('paint');
        
        if (navigation) {
          newMetrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
          newMetrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
          newMetrics.timeToInteractive = navigation.domInteractive - navigation.fetchStart;
        }

        if (paint.length > 0) {
          const firstPaint = paint.find(entry => entry.name === 'first-paint');
          const firstContentfulPaint = paint.find(entry => entry.name === 'first-contentful-paint');
          
          if (firstPaint) newMetrics.firstPaint = firstPaint.startTime;
          if (firstContentfulPaint) newMetrics.largestContentfulPaint = firstContentfulPaint.startTime;
        }

        // Memory usage (if available)
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          newMetrics.memoryUsage = memory.usedJSHeapSize;
        }
      }

      // React Query metrics
      const queryCache = queryClient.getQueryCache();
      const queries = queryCache.getAll();
      
      newMetrics.queryCount = queries.length;
      newMetrics.staleCacheEntries = queries.filter(query => query.isStale()).length;
      
      // Estimate cache size (rough calculation)
      let cacheSize = 0;
      queries.forEach(query => {
        if (query.state.data) {
          try {
            cacheSize += JSON.stringify(query.state.data).length * 2; // Rough byte estimate
          } catch (e) {
            // Ignore circular references
          }
        }
      });
      newMetrics.cacheSize = cacheSize;

      // Network metrics (if Performance Observer is available)
      if (typeof PerformanceObserver !== 'undefined') {
        const resourceEntries = performance.getEntriesByType('resource');
        newMetrics.networkRequests = resourceEntries.length;
        newMetrics.failedRequests = resourceEntries.filter(entry => 
          (entry as PerformanceResourceTiming).transferSize === 0
        ).length;
      }

      // Update metrics
      const updatedMetrics = { ...metricsRef.current, ...newMetrics } as PerformanceMetrics;
      metricsRef.current = updatedMetrics;
      setMetrics(updatedMetrics);

      // Check for performance alerts
      checkPerformanceAlerts(updatedMetrics);

    } catch (error) {
      console.error('Error collecting performance metrics:', error);
    }
  }, [enabled, queryClient, thresholds]);

  // Check for performance issues and generate alerts
  const checkPerformanceAlerts = useCallback((currentMetrics: PerformanceMetrics) => {
    const newAlerts: PerformanceAlert[] = [];

    Object.entries(thresholds).forEach(([metric, threshold]) => {
      const value = currentMetrics[metric as keyof PerformanceMetrics];
      
      if (typeof value === 'number' && value > threshold) {
        const alert: PerformanceAlert = {
          type: getAlertType(metric as keyof PerformanceMetrics, value, threshold),
          message: `${metric} exceeded threshold: ${value.toFixed(2)} > ${threshold}`,
          metric: metric as keyof PerformanceMetrics,
          value,
          threshold,
          suggestion: getOptimizationSuggestion(metric as keyof PerformanceMetrics)
        };
        
        newAlerts.push(alert);
        onAlert?.(alert);
      }
    });

    setAlerts(newAlerts);
  }, [thresholds, onAlert]);

  // Get alert type based on severity
  const getAlertType = (metric: keyof PerformanceMetrics, value: number, threshold: number): 'warning' | 'error' | 'info' => {
    const ratio = value / threshold;
    
    if (ratio > 2) return 'error';
    if (ratio > 1.5) return 'warning';
    return 'info';
  };

  // Get optimization suggestions
  const getOptimizationSuggestion = (metric: keyof PerformanceMetrics): string => {
    const suggestions: Record<keyof PerformanceMetrics, string> = {
      loadTime: 'Consider code splitting, lazy loading, or reducing bundle size',
      domContentLoaded: 'Optimize critical rendering path and reduce blocking resources',
      firstPaint: 'Minimize render-blocking resources and optimize CSS delivery',
      largestContentfulPaint: 'Optimize images, fonts, and critical resources',
      firstInputDelay: 'Reduce JavaScript execution time and use web workers',
      cumulativeLayoutShift: 'Set explicit dimensions for images and avoid dynamic content insertion',
      queryCount: 'Consider query deduplication and cleanup unused queries',
      cacheSize: 'Implement cache cleanup strategies and reduce cached data size',
      staleCacheEntries: 'Implement better cache invalidation strategies',
      memoryUsage: 'Check for memory leaks and optimize data structures',
      networkRequests: 'Implement request batching and reduce unnecessary API calls',
      failedRequests: 'Implement better error handling and retry mechanisms',
      timeToInteractive: 'Optimize JavaScript bundle size and execution',
      adminPageLoadTime: 'Implement lazy loading for admin components',
      listRenderTime: 'Use virtual scrolling for large lists',
      formSubmissionTime: 'Optimize form validation and submission logic'
    };
    
    return suggestions[metric] || 'Review and optimize this metric';
  };

  // Report metrics to server
  const reportMetrics = useCallback(async () => {
    if (!enabled || !metricsRef.current.loadTime) return;

    try {
      await fetch('/api/admin/performance/client-metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          page_name: window.location.pathname,
          timestamp: Date.now(),
          metrics: metricsRef.current,
          alerts: alerts.length,
          user_agent: navigator.userAgent,
        }),
      });
    } catch (error) {
      console.error('Error reporting performance metrics:', error);
    }
  }, [enabled, alerts.length]);

  // Set up metric collection interval
  useEffect(() => {
    if (!enabled) return;

    // Initial collection
    collectMetrics();

    // Set up intervals
    collectIntervalRef.current = setInterval(collectMetrics, collectInterval);
    reportIntervalRef.current = setInterval(reportMetrics, reportInterval);

    return () => {
      if (collectIntervalRef.current) clearInterval(collectIntervalRef.current);
      if (reportIntervalRef.current) clearInterval(reportIntervalRef.current);
    };
  }, [enabled, collectMetrics, reportMetrics, collectInterval, reportInterval]);

  // Manual performance check
  const runPerformanceCheck = useCallback(() => {
    collectMetrics();
  }, [collectMetrics]);

  // Clear alerts
  const clearAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  return {
    metrics,
    alerts,
    runPerformanceCheck,
    clearAlerts,
    isMonitoring: enabled,
  };
}
