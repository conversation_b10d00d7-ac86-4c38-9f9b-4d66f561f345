import { useEffect, useRef, useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { queryKeys, cacheInvalidation } from './useQueryConfig';

/**
 * Smart real-time update system with adaptive polling and intelligent cache invalidation
 * This system adapts polling intervals based on activity and provides targeted updates
 */

// Legacy interface for backward compatibility
interface UseRealTimeUpdatesOptions {
  interval?: number;
  enabled?: boolean;
  queryKeys?: string[][];
  pauseWhenHidden?: boolean;
}

interface UseSmartRealTimeUpdatesOptions {
  /**
   * Base interval in milliseconds for checking updates
   * Default: 30000 (30 seconds) - more reasonable for production
   */
  baseInterval?: number;

  /**
   * Whether to enable real-time updates
   * Default: true
   */
  enabled?: boolean;

  /**
   * Resource types to monitor for updates
   * Default: all admin resources
   */
  resources?: string[];

  /**
   * Whether to use visibility API to pause when tab is not visible
   * Default: true
   */
  pauseWhenHidden?: boolean;

  /**
   * Whether to use adaptive polling (increases interval when no changes detected)
   * Default: true
   */
  useAdaptivePolling?: boolean;

  /**
   * Maximum interval for adaptive polling (in milliseconds)
   * Default: 300000 (5 minutes)
   */
  maxInterval?: number;

  /**
   * Whether to use WebSocket for real-time updates when available
   * Default: false (not implemented yet)
   */
  useWebSocket?: boolean;
}

export function useRealTimeUpdates(options: UseRealTimeUpdatesOptions = {}) {
  const {
    interval = 5000, // 5 seconds
    enabled = true,
    queryKeys = [
      ['public-galleries'],
      ['public-gallery'],
      ['public-trips'],
      ['public-trip'],
      ['public-photo-albums'],
      ['public-blogs'],
      ['public-blog']
    ],
    pauseWhenHidden = true
  } = options;

  const queryClient = useQueryClient();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isVisibleRef = useRef(true);

  // Track page visibility
  useEffect(() => {
    if (!pauseWhenHidden) return;

    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden;
      
      // If page becomes visible, immediately invalidate queries
      if (!document.hidden && enabled) {
        queryKeys.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey });
        });
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [queryClient, queryKeys, enabled, pauseWhenHidden]);

  // Set up real-time update interval
  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    const performUpdate = () => {
      // Only update if page is visible (unless pauseWhenHidden is false)
      if (pauseWhenHidden && !isVisibleRef.current) {
        return;
      }

      // Invalidate all specified query keys
      queryKeys.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey });
      });
    };

    // Set up interval
    intervalRef.current = setInterval(performUpdate, interval);

    // Cleanup on unmount or dependency change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [queryClient, queryKeys, interval, enabled, pauseWhenHidden]);

  // Manual trigger function
  const triggerUpdate = () => {
    queryKeys.forEach(queryKey => {
      queryClient.invalidateQueries({ queryKey });
    });
  };

  return {
    triggerUpdate,
    isEnabled: enabled,
    interval
  };
}

/**
 * Hook specifically for public pages that need real-time updates
 * This provides a more aggressive update strategy for public-facing content
 */
export function usePublicRealTimeUpdates(enabled: boolean = true, customInterval?: number) {
  return useRealTimeUpdates({
    interval: customInterval || 30000, // 30 seconds - more reasonable for production
    enabled,
    queryKeys: [
      ['public-galleries'],
      ['public-gallery'],
      ['public-trips'],
      ['public-trip'],
      ['public-photo-albums'],
      ['public-blogs'],
      ['public-blog']
    ],
    pauseWhenHidden: true // Pause when tab is not visible to save resources
  });
}

/**
 * Hook for admin pages to trigger immediate updates
 * This can be used in admin components to force immediate cache invalidation
 */
export function useAdminRealTimeUpdates() {
  const queryClient = useQueryClient();

  const forceUpdate = () => {
    // Nuclear option - clear all caches
    queryClient.clear();
    
    // Or more targeted approach
    const publicQueryKeys = [
      ['public-galleries'],
      ['public-gallery'],
      ['public-trips'],
      ['public-trip'],
      ['public-photo-albums'],
      ['public-blogs'],
      ['public-blog']
    ];

    publicQueryKeys.forEach(queryKey => {
      queryClient.invalidateQueries({ queryKey });
      queryClient.refetchQueries({ queryKey });
    });
  };

  const softUpdate = () => {
    // Gentler approach - just invalidate
    const publicQueryKeys = [
      ['public-galleries'],
      ['public-gallery'],
      ['public-trips'],
      ['public-trip'],
      ['public-photo-albums'],
      ['public-blogs'],
      ['public-blog']
    ];

    publicQueryKeys.forEach(queryKey => {
      queryClient.invalidateQueries({ queryKey });
    });
  };

  return {
    forceUpdate,
    softUpdate
  };
}

/**
 * Smart real-time updates hook with adaptive polling and intelligent cache invalidation
 */
export function useSmartRealTimeUpdates({
  baseInterval = 30000, // 30 seconds default
  enabled = true,
  resources = ['trips', 'blogs', 'inquiries', 'photos', 'team-members'],
  pauseWhenHidden = true,
  useAdaptivePolling = true,
  maxInterval = 300000, // 5 minutes max
  useWebSocket = false
}: UseSmartRealTimeUpdatesOptions = {}) {
  const queryClient = useQueryClient();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isVisibleRef = useRef(true);
  const [currentInterval, setCurrentInterval] = useState(baseInterval);
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());
  const consecutiveNoChanges = useRef(0);

  // Track page visibility
  useEffect(() => {
    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden;
      if (!document.hidden && enabled) {
        // Reset interval when page becomes visible
        setCurrentInterval(baseInterval);
        consecutiveNoChanges.current = 0;
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [baseInterval, enabled]);

  // Smart update function that checks for actual changes
  const performSmartUpdate = useCallback(async () => {
    if (!enabled || (pauseWhenHidden && !isVisibleRef.current)) {
      return;
    }

    try {
      // Get current cache state before invalidation
      const cacheSnapshot = new Map();
      resources.forEach(resource => {
        const queryKey = [resource];
        const cachedData = queryClient.getQueryData(queryKey);
        cacheSnapshot.set(resource, cachedData);
      });

      // Invalidate and refetch specific resources
      const invalidationPromises = resources.map(async (resource) => {
        const queryKey = [resource];
        await queryClient.invalidateQueries({ queryKey });
        return queryClient.refetchQueries({ queryKey });
      });

      await Promise.all(invalidationPromises);

      // Check if data actually changed
      let hasChanges = false;
      resources.forEach(resource => {
        const queryKey = [resource];
        const newData = queryClient.getQueryData(queryKey);
        const oldData = cacheSnapshot.get(resource);

        if (JSON.stringify(newData) !== JSON.stringify(oldData)) {
          hasChanges = true;
        }
      });

      // Adaptive polling logic
      if (useAdaptivePolling) {
        if (hasChanges) {
          // Reset to base interval when changes are detected
          consecutiveNoChanges.current = 0;
          setCurrentInterval(baseInterval);
          setLastUpdateTime(Date.now());
        } else {
          // Increase interval when no changes are detected
          consecutiveNoChanges.current += 1;

          if (consecutiveNoChanges.current >= 3) {
            const newInterval = Math.min(currentInterval * 1.5, maxInterval);
            setCurrentInterval(newInterval);
          }
        }
      }

      // Also invalidate dashboard stats when any resource changes
      if (hasChanges) {
        queryClient.invalidateQueries({ queryKey: queryKeys.dashboard });
      }

    } catch (error) {
      console.error('Smart real-time update error:', error);
      // On error, back off polling frequency
      if (useAdaptivePolling) {
        const newInterval = Math.min(currentInterval * 2, maxInterval);
        setCurrentInterval(newInterval);
      }
    }
  }, [enabled, pauseWhenHidden, resources, queryClient, useAdaptivePolling, baseInterval, maxInterval, currentInterval]);

  // Set up smart polling interval
  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Set up new interval with current adaptive interval
    intervalRef.current = setInterval(performSmartUpdate, currentInterval);

    // Cleanup on unmount or dependency change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [performSmartUpdate, currentInterval, enabled]);

  // Manual refresh function
  const manualRefresh = useCallback(() => {
    setCurrentInterval(baseInterval);
    consecutiveNoChanges.current = 0;
    performSmartUpdate();
  }, [baseInterval, performSmartUpdate]);

  return {
    currentInterval,
    lastUpdateTime,
    manualRefresh,
    isAdaptive: useAdaptivePolling,
    consecutiveNoChanges: consecutiveNoChanges.current
  };
}

/**
 * Hook for admin panels with optimized real-time updates
 * Uses smart polling with adaptive intervals and targeted cache invalidation
 */
export function useAdminSmartUpdates(resources?: string[], enabled: boolean = true) {
  return useSmartRealTimeUpdates({
    baseInterval: 45000, // 45 seconds for admin panels
    enabled,
    resources: resources || ['trips', 'blogs', 'inquiries', 'photos', 'team-members', 'admin-users'],
    pauseWhenHidden: true,
    useAdaptivePolling: true,
    maxInterval: 300000, // 5 minutes max
    useWebSocket: false // TODO: Implement WebSocket support
  });
}

/**
 * Hook for dashboard with optimized polling
 */
export function useDashboardSmartUpdates(enabled: boolean = true) {
  return useSmartRealTimeUpdates({
    baseInterval: 60000, // 1 minute for dashboard stats
    enabled,
    resources: ['dashboard-stats'],
    pauseWhenHidden: true,
    useAdaptivePolling: true,
    maxInterval: 600000, // 10 minutes max for dashboard
    useWebSocket: false
  });
}
